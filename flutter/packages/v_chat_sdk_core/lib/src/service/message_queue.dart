// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/src/exceptions/http/v_chat_http_exception.dart';
import 'package:v_chat_sdk_core/src/models/v_chat_base_exception.dart';
import 'package:v_chat_sdk_core/src/utils/api_constants.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

class VMessageUploaderQueue {
  final _uploadQueue = <VMessageUploadModel>[];
  final _localStorage = VChatController.I.nativeApi.local.message;
  final _remoteStorage = VChatController.I.nativeApi.remote;
  final _log = Logger("VMessageUploaderQueue");

  /// Singleton pattern
  VMessageUploaderQueue._();

  /// Singleton instance
  static final _instance = VMessageUploaderQueue._();

  /// Getter for singleton instance
  static VMessageUploaderQueue get instance {
    return _instance;
  }

  void removeFromQueue(String localId) {
    _uploadQueue.removeWhere((e) => e.msgLocalId == localId);
  }

  /// Add a message to the queue and send it to the API
  /// [uploadModel] is the model of the message to be uploaded
  Future<void> addToQueue(VMessageUploadModel uploadModel) async {
    if (VPlatforms.isMobile) {
      await _moveFile(uploadModel.baseMessage);
    }
    if (!_uploadQueue.contains(uploadModel)) {
      _uploadQueue.add(uploadModel);
      if (VPlatforms.isMobile && uploadModel.baseMessage is VUploadMessage) {
        await _sendToMobileApi(uploadModel);
        return;
      }
      await _sendToWebApi(uploadModel);
    }
  }

  Future<void> _moveFile(VBaseMessage localMsg) async {
    if (localMsg is VDownloadableMessage) {
      final dMessage = localMsg as VDownloadableMessage;
      if (localMsg.isForward) return;
      await VFileUtils.copyFileToAppFolder(
        dMessage.localFilePathWithExt,
        dMessage.fileSource.fileLocalPath!,
      );
    }
  }

  /// Send the message to the API, handle errors and remove message from queue
  /// [uploadModel] is the model of the message to be sent
  Future<void> _sendToWebApi(VMessageUploadModel uploadModel) async {
    await _setSending(uploadModel);
    try {
      final msg = await _remoteStorage.message.createMessage(uploadModel);
      await _onSuccessToSend(msg);
    } catch (e) {
      if (e is VChatHttpForbidden) {
        if (kDebugMode) {
          print(e);
        }
        _log.warning("VChatBaseHttpException", e);
        await _deleteTheMessage(uploadModel);
      } else if (e is VChatBaseHttpException) {
        _log.info("VChatBaseHttpException", e);
        await _setErrorToMessage(uploadModel);
      } else if (e is VUserInternetException) {
        _log.info("UserInternetExceptionUserInternetException", e);
        await _setErrorToMessage(uploadModel);
      } else {
        _log.warning("_onUnknownException", e);
        // await _deleteTheMessage(uploadModel);
      }
    } finally {
      _uploadQueue.remove(uploadModel);
    }
  }

  Future<void> _sendToMobileApi(VMessageUploadModel uploadModel) async {
    _setSending(uploadModel);
    final base = uploadModel.baseMessage;
    final message = uploadModel.baseMessage as VUploadMessage;

    // CRITICAL DEBUG: Log upload details for one-time messages
    if (base.isOneSeen) {
      print("📤 UPLOAD DEBUG for one-time message:");
      print("  localId: ${base.localId}");
      print("  isOneSeen: ${base.isOneSeen}");
      print("  isMeSender: ${base.isMeSender}");
      print("  messageType: ${base.messageType}");
      print("  fields: ${uploadModel.getMapFromPartValuesUsingMap()}");
    }

    final oldTask = await FileDownloader().taskForId(base.localId);
    if (oldTask != null) {
      //this means this task already run!!
      _log.warning("This means this task already run!!");
      if (base.isOneSeen) {
        print(
            "❌ UPLOAD BLOCKED: Task already exists for one-time message ${base.localId}");
      }
      return;
    }
    final access = VAppPref.getHashedString(
      key: SStorageKeys.vAccessToken.name,
    );

    final fields = uploadModel.getMapFromPartValuesUsingMap();
    if (base.isOneSeen) {
      print("📋 UPLOAD FIELDS for one-time message: $fields");
    }

    final task = UploadTask(
      //base/api/v2                /channel/message
      url: "${VAppConstants.baseUri}/channel/${base.roomId}/message",
      filename: message.localFilePathWithExt,
      displayName: _getUploadGroupName(base),
      group: _getUploadGroupName(base),
      updates: Updates.statusAndProgress,
      // retries: 5,
      //add meta data here
      fields: fields,
      //add access token
      headers: {
        "authorization": "Bearer $access",
      },
      httpRequestMethod: "POST",
      metaData: base.roomId,
      directory: "media",
      taskId: base.localId,
    );

    if (base.isOneSeen) {
      print("🚀 ENQUEUEING upload task for one-time message ${base.localId}");
    }

    FileDownloader().enqueue(task);
  }

  /// Delete a message by its local id
  /// [localId] is the local id of the message to be deleted
  Future<void> _deleteMessage(String localId) async {
    final baseMessage = await _localStorage.getMessageByLocalId(localId);
    if (baseMessage != null && !baseMessage.emitStatus.isServerConfirm) {
      await _localStorage.deleteMessageByLocalId(baseMessage);
    }
  }

  /// Set the message status to error
  /// [uploadModel] is the model of the message with an error
  Future _setErrorToMessage(VMessageUploadModel uploadModel) async {
    final VBaseMessage? baseMessage =
        await _localStorage.getMessageByLocalId(uploadModel.msgLocalId);
    if (baseMessage != null) {
      baseMessage.emitStatus = VMessageEmitStatus.error;
      await _localStorage.updateMessageSendingStatus(
        VUpdateMessageStatusEvent(
          roomId: baseMessage.roomId,
          localId: baseMessage.localId,
          emitState: baseMessage.emitStatus,
        ),
      );
    }
  }

  /// Delete a message from the queue
  /// [uploadModel] is the model of the message to be deleted
  Future _deleteTheMessage(VMessageUploadModel uploadModel) async {
    await _deleteMessage(uploadModel.msgLocalId);
  }

  /// Update the local message status to success upon successful send
  /// [messageModel] is the model of the successfully sent message
  Future _onSuccessToSend(VBaseMessage messageModel) async {
    await _localStorage.updateFullMessage(messageModel);
  }

  /// Clear the queue of all messages
  void clearQueue() {
    _uploadQueue.clear();
  }

  /// Set the message status to sending
  /// [uploadModel] is the model of the message that is being sent
  Future<void> _setSending(VMessageUploadModel uploadModel) async {
    await _localStorage.updateMessageSendingStatus(
      VUpdateMessageStatusEvent(
        roomId: uploadModel.roomId,
        localId: uploadModel.msgLocalId,
        emitState: VMessageEmitStatus.sending,
      ),
    );
  }

  String _getUploadGroupName(VBaseMessage base) {
    if (base is VFileMessage) return "files";
    if (base is VVideoMessage) return "files";
    return "images";
  }
}
