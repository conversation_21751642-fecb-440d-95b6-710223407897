// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

import '../../../../../v_chat_message_page.dart';
import '../../providers/message_provider.dart';

class MessageStateController extends ValueNotifier<List<VBaseMessage>>
    with VSocketStatusStream {
  final VRoom vRoom;
  final MessageProvider messageProvider;
  final AutoScrollController scrollController;
  LoadMoreStatus _loadingStatus = LoadMoreStatus.loaded;

  // CRITICAL FIX: Cache for one-time messages sent by current user
  // This prevents server responses from overwriting local one-time message state
  final Set<String> _oneTimeMessageLocalIds = <String>{};

  MessageStateController({
    required this.vRoom,
    required this.messageProvider,
    required this.scrollController,
  }) : super(<VBaseMessage>[]) {
    initSocketStatusStream(
      VChatController.I.nativeApi.streams.socketStatusStream,
    );
    getMessagesFromLocal();
    unawaited(getMessagesFromRemote(_initFilterDto));
    scrollController.addListener(_loadMoreListener);
  }

  BuildContext get context => VChatController.I.navigationContext;

  void _initLoadMore() {
    _loadingStatus = LoadMoreStatus.loaded;
    _filterDto.lastId = null;
  }

  List<VBaseMessage> get stateMessages => value;
  final messageStateStream = StreamController<VBaseMessage>.broadcast();

  bool get isMessagesEmpty => stateMessages.isEmpty;

  String get lastMessageId => stateMessages.last.id;
  final _initFilterDto = VRoomMessagesDto(
    limit: 30,
    lastId: null,
  );
  final _filterDto = VRoomMessagesDto(
    limit: 30,
    lastId: null,
  );

  void insertAllMessages(List<VBaseMessage> messages) {
    value = sort(messages);
  }

  void updateApiMessages(List<VBaseMessage> apiMessages) {
    if (apiMessages.isEmpty) return;

    if (kDebugMode) {
      print("📥 API REFRESH: Received ${apiMessages.length} messages");
      for (final msg in apiMessages) {
        if (msg.isOneSeen) {
          print(
              "  📥 API message: ${msg.localId}, isOneSeen: ${msg.isOneSeen}, isMeSender: ${msg.isMeSender}");
        }
      }
      print(
          "🔒 Currently tracking ${_oneTimeMessageLocalIds.length} one-time messages: $_oneTimeMessageLocalIds");
    }

    final newList = <VBaseMessage>[];
    final stateMessages = value;

    // Combine apiMessages and sending/error messages from stateMessages
    newList.addAll(apiMessages);
    newList.addAll(stateMessages.where((e) => e.emitStatus.isSendingOrError));

    // Replace updated messages in newList
    for (final localDbMessage in stateMessages) {
      if (localDbMessage.contentTr != null || localDbMessage.isDownloading) {
        final index =
            newList.indexWhere((element) => element.id == localDbMessage.id);
        if (index != -1) {
          newList[index] = localDbMessage;
        }
      }
    }

    // CRITICAL FIX: Preserve one-time messages sent by current user
    // Check if any API messages are overwriting our tracked one-time messages
    for (int i = 0; i < newList.length; i++) {
      final apiMessage = newList[i];
      if (_oneTimeMessageLocalIds.contains(apiMessage.localId)) {
        if (kDebugMode) {
          print(
              "🛡️ PROTECTING tracked one-time message: ${apiMessage.localId}");
        }
        // Find the original local message to preserve its state
        final localMessage = stateMessages.firstWhere(
          (local) => local.localId == apiMessage.localId,
          orElse: () => apiMessage,
        );
        // If we found the local message and it's a one-time message from current user, preserve it
        if (localMessage.isOneSeen && localMessage.isMeSender) {
          newList[i] = localMessage;
          if (kDebugMode) {
            print(
                "✅ PRESERVED one-time message state for: ${apiMessage.localId}");
          }
        }
      } else if (apiMessage.isOneSeen && apiMessage.isMeSender) {
        // ADDITIONAL FIX: If this is a one-time message from current user that we haven't tracked yet,
        // it might be coming from the server after upload. Add it to tracking and preserve its state.
        _oneTimeMessageLocalIds.add(apiMessage.localId);
        if (kDebugMode) {
          print(
              "🔒 LATE TRACKING one-time message from API: ${apiMessage.localId}");
        }
      }
    }

    // Sort and update value only if context is still mounted
    if (context.mounted) {
      value = sort(newList);
    }
  }

  List<VBaseMessage> sort(List<VBaseMessage> messages) {
    messages.sort((a, b) {
      return b.id.compareTo(a.id);
    });
    return messages;
  }

  void insertMessage(VBaseMessage messageModel) {
    if (!stateMessages.contains(messageModel)) {
      // CRITICAL FIX: Track one-time messages sent by current user
      if (messageModel.isOneSeen && messageModel.isMeSender) {
        _oneTimeMessageLocalIds.add(messageModel.localId);
        if (kDebugMode) {
          print(
              "🔒 TRACKING one-time message: ${messageModel.localId}, isOneSeen: ${messageModel.isOneSeen}, isMeSender: ${messageModel.isMeSender}");
        }
      }

      value.insert(0, messageModel);
      notifyListeners();
    } else {
      if (kDebugMode) {
        print(
            "-------------you are try to insert message which already exist!-----------");
      }
    }
  }

  void updateMessage(VBaseMessage messageModel) {
    final msgIndex = stateMessages.indexOf(messageModel);
    if (msgIndex != -1) {
      // CRITICAL FIX: Protect tracked one-time messages from being overwritten
      if (_oneTimeMessageLocalIds.contains(messageModel.localId)) {
        if (kDebugMode) {
          print(
              "🛡️ UPDATE PROTECTION for tracked message: ${messageModel.localId}, isOneSeen: ${messageModel.isOneSeen}, isMeSender: ${messageModel.isMeSender}");
        }
        // Only update if the new message maintains the one-time state
        if (messageModel.isOneSeen && messageModel.isMeSender) {
          value[msgIndex] = messageModel;
          messageStateStream.sink.add(messageModel);
          if (kDebugMode) {
            print(
                "✅ ALLOWED update for one-time message: ${messageModel.localId}");
          }
        } else {
          if (kDebugMode) {
            print(
                "🚫 BLOCKED update for one-time message: ${messageModel.localId}");
          }
        }
        // Otherwise, keep the existing message to preserve one-time state
        return;
      }

      //full update for non-tracked messages
      value[msgIndex] = messageModel;
      messageStateStream.sink.add(messageModel);
    } else {
      if (kDebugMode) {
        print(
            "----------------you are try to update message which Not exist!--------------");
      }
    }
  }

  void close() {
    messageStateStream.close();
    dispose();
    closeSocketStatusStream();
  }

  int _indexByLocalId(String localId) =>
      value.indexWhere((e) => e.localId == localId);

  void deleteMessage(String localId) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].isDeleted = true;
      messageStateStream.add(value[index]);
    }
  }

  void updateMessageStatus(String localId, VMessageEmitStatus emitState) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].emitStatus = emitState;
      messageStateStream.add(value[index]);
    }
  }

  void updateMessageStar(String localId, VUpdateMessageStarEvent event) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].isStared = event.isStar;
      messageStateStream.add(value[index]);
    }
  }

  void updateMessageOneSeen(String localId, VUpdateMessageOneSeenEvent event) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].isOneSeenByMe = true;
      messageStateStream.add(value[index]);
    }
  }

  void updateDownloadProgress(String localId, double progress) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].progress = progress;
      messageStateStream.add(value[index]);
    }
  }

  void updateMessageAllDeletedAt(String localId, String? allDeletedAt) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].allDeletedAt = allDeletedAt;
      messageStateStream.add(value[index]);
    }
  }

  void seenAll(VSocketOnRoomSeenModel model) {
    for (int i = 0; i < stateMessages.length; i++) {
      stateMessages[i].seenAt ??= model.date;
      stateMessages[i].deliveredAt ??= model.date;
    }
    notifyListeners();
  }

  void deliverAll(VSocketOnDeliverMessagesModel model) {
    for (int i = 0; i < stateMessages.length; i++) {
      stateMessages[i].deliveredAt ??= model.date;
    }
    notifyListeners();
  }

  @override
  void onSocketConnected() {
    getMessagesFromRemote(_initFilterDto);
    messageProvider.setSeen(vRoom.id);
  }

  Future<void> getMessagesFromRemote(VRoomMessagesDto dto) async {
    await VChatController.I.nativeApi.remote.socketIo.socketCompleter.future;
    await vSafeApiCall<List<VBaseMessage>>(
      request: () async {
        return messageProvider.getApiMessages(
          roomId: vRoom.id,
          dto: dto,
        );
      },
      onSuccess: (response) {
        updateApiMessages(response);
        VDownloaderService.instance.checkIfCanAutoDownloadFor(response);
      },
    );
  }

  Future<void> getMessagesFromLocal() async {
    await vSafeApiCall<List<VBaseMessage>>(
      request: () async {
        return messageProvider.getLocalMessages(
          roomId: vRoom.id,
          filter: _initFilterDto,
        );
      },
      onSuccess: (response) {
        insertAllMessages(response);
        VDownloaderService.instance.checkIfCanAutoDownloadFor(response);
      },
    );
  }

  void emitSeenFor(String roomId) {
    messageProvider.setSeen(roomId);
  }

  bool get requireLoadMoreMessages =>
      _loadingStatus != LoadMoreStatus.loading &&
      _loadingStatus != LoadMoreStatus.completed;

  void _loadMoreListener() async {
    final maxScrollExtent = scrollController.position.maxScrollExtent / 2;
    if (scrollController.offset > maxScrollExtent && requireLoadMoreMessages) {
      await loadMoreMessages();
    }
  }

  Future<List<VBaseMessage>?> loadMoreMessages() async {
    _loadingStatus = LoadMoreStatus.loading;
    _filterDto.lastId = value.last.id;
    final localLoadedMessages = await messageProvider.getLocalMessages(
      roomId: vRoom.id,
      filter: _filterDto,
    );
    if (localLoadedMessages.isEmpty) {
      ///if no more data ask server for it
      return await vSafeApiCall<List<VBaseMessage>>(
        request: () async {
          return messageProvider.getApiMessages(
            roomId: vRoom.id,
            dto: _filterDto,
          );
        },
        onSuccess: (response) {
          if (response.isEmpty) {
            _loadingStatus = LoadMoreStatus.completed;
            return null;
          }
          _loadingStatus = LoadMoreStatus.loaded;
          value.addAll(response);
          VDownloaderService.instance.checkIfCanAutoDownloadFor(response);
          notifyListeners();
          return response;
        },
      );
    }
    _loadingStatus = LoadMoreStatus.loaded;
    value.addAll(localLoadedMessages);
    notifyListeners();
    return localLoadedMessages;
  }

  Future<void> loadUntil(VBaseMessage message) async {
    await vSafeApiCall<List<VBaseMessage>>(
      request: () async {
        return messageProvider.getLocalMessages(
          roomId: vRoom.id,
          filter: VRoomMessagesDto(
            between: VMessageBetweenFilter(
              lastId: value.last.id,
              targetId: message.id,
            ),
          ),
        );
      },
      onSuccess: (response) {
        value.insertAll(0, response);
        notifyListeners();
      },
    );
  }

  void messageSearch(String text) async {
    final searchMessages = await messageProvider.search(vRoom.id, text);
    value = searchMessages;
    notifyListeners();
  }

  void resetMessages() {
    _initLoadMore();
    getMessagesFromLocal();
  }

  void updateIsDownloading(String localId, bool isDownload) {
    final index = _indexByLocalId(localId);
    if (index != -1) {
      value[index].isDownloading = isDownload;
      messageStateStream.add(value[index]);
    }
  }
}
